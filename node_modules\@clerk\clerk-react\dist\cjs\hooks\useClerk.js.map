{"version": 3, "sources": ["../../../src/hooks/useClerk.ts"], "sourcesContent": ["import type { LoadedClerk } from '@clerk/types';\n\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\n\nexport const useClerk = (): LoadedClerk => {\n  const isomorphicClerk = useIsomorphicClerkContext();\n  // The actual value is an instance of IsomorphicClerk, not Clerk\n  // we expose is as a Clerk instance\n  return isomorphicClerk as unknown as LoadedClerk;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oCAA0C;AAEnC,MAAM,WAAW,MAAmB;AACzC,QAAM,sBAAkB,yDAA0B;AAGlD,SAAO;AACT;", "names": []}