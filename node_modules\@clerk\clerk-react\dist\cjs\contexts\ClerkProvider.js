"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var ClerkProvider_exports = {};
__export(ClerkProvider_exports, {
  ClerkProvider: () => ClerkProvider,
  __internal__setErrorThrowerOptions: () => import_utils.__internal__setErrorThrowerOptions
});
module.exports = __toCommonJS(ClerkProvider_exports);
var import_keys = require("@clerk/shared/keys");
var import_react = __toESM(require("react"));
var import_errors = require("../errors");
var import_utils = require("../utils");
var import_ClerkContextProvider = require("./ClerkContextProvider");
var import_StructureContext = require("./StructureContext");
(0, import_utils.__internal__setErrorThrowerOptions)({
  packageName: "@clerk/clerk-react"
});
function ClerkProviderBase(props) {
  const { initialState, children, ...restIsomorphicClerkOptions } = props;
  const { frontendApi = "", publishableKey = "", Clerk: userInitialisedClerk } = restIsomorphicClerkOptions;
  if (!userInitialisedClerk) {
    if (!publishableKey && !frontendApi) {
      import_utils.errorThrower.throwMissingPublishableKeyError();
    } else if (publishableKey && !(0, import_keys.isPublishableKey)(publishableKey)) {
      import_utils.errorThrower.throwInvalidPublishableKeyError({ key: publishableKey });
    } else if (!publishableKey && frontendApi && !(0, import_keys.isLegacyFrontendApiKey)(frontendApi)) {
      import_utils.errorThrower.throwInvalidFrontendApiError({ key: frontendApi });
    }
  }
  return /* @__PURE__ */ import_react.default.createElement(import_StructureContext.StructureContext.Provider, { value: import_StructureContext.StructureContextStates.noGuarantees }, /* @__PURE__ */ import_react.default.createElement(
    import_ClerkContextProvider.ClerkContextProvider,
    {
      initialState,
      isomorphicClerkOptions: restIsomorphicClerkOptions
    },
    children
  ));
}
const ClerkProvider = (0, import_utils.withMaxAllowedInstancesGuard)(ClerkProviderBase, "ClerkProvider", import_errors.multipleClerkProvidersError);
ClerkProvider.displayName = "ClerkProvider";
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  ClerkProvider,
  __internal__setErrorThrowerOptions
});
//# sourceMappingURL=ClerkProvider.js.map