{"version": 3, "sources": ["../../../src/utils/versionSelector.ts"], "sourcesContent": ["/**\n * This version selector is a bit complicated, so here is the flow:\n * 1. Use the clerkJSVersion prop on the provider\n * 2. Use the exact `@clerk/clerk-js` version if it is a `@snapshot` prerelease for `@clerk/clerk-react`\n * 3. Use the prerelease tag of `@clerk/clerk-react`\n * 4. Fallback to the major version of `@clerk/clerk-react`\n * @param clerkJSVersion - The optional clerkJSVersion prop on the provider\n * @returns The npm tag, version or major version to use\n */\nexport const versionSelector = (clerkJSVersion: string | undefined) => {\n  if (clerkJSVersion) {\n    return clerkJSVersion;\n  }\n\n  const prereleaseTag = getPrereleaseTag(PACKAGE_VERSION);\n  if (prereleaseTag) {\n    if (prereleaseTag === 'snapshot') {\n      return JS_PACKAGE_VERSION;\n    }\n\n    return prereleaseTag;\n  }\n\n  return getMajorVersion(PACKAGE_VERSION);\n};\n\n// TODO: Replace these with \"semver\" package\nconst getPrereleaseTag = (packageVersion: string) => packageVersion.match(/-(.*)\\./)?.[1];\nconst getMajorVersion = (packageVersion: string) => packageVersion.split('.')[0];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AASO,MAAM,kBAAkB,CAAC,mBAAuC;AACrE,MAAI,gBAAgB;AAClB,WAAO;AAAA,EACT;AAEA,QAAM,gBAAgB,iBAAiB,QAAe;AACtD,MAAI,eAAe;AACjB,QAAI,kBAAkB,YAAY;AAChC,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,gBAAgB,QAAe;AACxC;AAGA,MAAM,mBAAmB,CAAC,mBAAwB;AA3BlD;AA2BqD,8BAAe,MAAM,SAAS,MAA9B,mBAAkC;AAAA;AACvF,MAAM,kBAAkB,CAAC,mBAA2B,eAAe,MAAM,GAAG,EAAE,CAAC;", "names": []}