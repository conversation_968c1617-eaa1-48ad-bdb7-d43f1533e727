{"version": 3, "sources": ["../../../src/hooks/utils.ts"], "sourcesContent": ["import type IsomorphicClerk from '../isomorphicClerk';\n\n/**\n * @internal\n */\nconst clerkLoaded = (isomorphicClerk: IsomorphicClerk) => {\n  return new Promise<void>(resolve => {\n    if (isomorphicClerk.loaded) {\n      resolve();\n    }\n    isomorphicClerk.addOnLoaded(resolve);\n  });\n};\n\n/**\n * @internal\n */\nexport const createGetToken = (isomorphicClerk: IsomorphicClerk) => {\n  return async (options: any) => {\n    await clerkLoaded(isomorphicClerk);\n    if (!isomorphicClerk.session) {\n      return null;\n    }\n    return isomorphicClerk.session.getToken(options);\n  };\n};\n\n/**\n * @internal\n */\nexport const createSignOut = (isomorphicClerk: IsomorphicClerk) => {\n  return async (...args: any) => {\n    await clerkLoaded(isomorphicClerk);\n    return isomorphicClerk.signOut(...args);\n  };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,MAAM,cAAc,CAAC,oBAAqC;AACxD,SAAO,IAAI,QAAc,aAAW;AAClC,QAAI,gBAAgB,QAAQ;AAC1B,cAAQ;AAAA,IACV;AACA,oBAAgB,YAAY,OAAO;AAAA,EACrC,CAAC;AACH;AAKO,MAAM,iBAAiB,CAAC,oBAAqC;AAClE,SAAO,OAAO,YAAiB;AAC7B,UAAM,YAAY,eAAe;AACjC,QAAI,CAAC,gBAAgB,SAAS;AAC5B,aAAO;AAAA,IACT;AACA,WAAO,gBAAgB,QAAQ,SAAS,OAAO;AAAA,EACjD;AACF;AAKO,MAAM,gBAAgB,CAAC,oBAAqC;AACjE,SAAO,UAAU,SAAc;AAC7B,UAAM,YAAY,eAAe;AACjC,WAAO,gBAAgB,QAAQ,GAAG,IAAI;AAAA,EACxC;AACF;", "names": []}