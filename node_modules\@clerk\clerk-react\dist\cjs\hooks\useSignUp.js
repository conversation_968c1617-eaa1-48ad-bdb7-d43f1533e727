"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var useSignUp_exports = {};
__export(useSignUp_exports, {
  useSignUp: () => useSignUp
});
module.exports = __toCommonJS(useSignUp_exports);
var import_ClientContext = require("../contexts/ClientContext");
var import_IsomorphicClerkContext = require("../contexts/IsomorphicClerkContext");
const useSignUp = () => {
  const isomorphicClerk = (0, import_IsomorphicClerkContext.useIsomorphicClerkContext)();
  const client = (0, import_ClientContext.useClientContext)();
  if (!client) {
    return { isLoaded: false, signUp: void 0, setSession: void 0, setActive: void 0 };
  }
  return {
    isLoaded: true,
    signUp: client.signUp,
    setSession: isomorphicClerk.setSession,
    setActive: isomorphicClerk.setActive
  };
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  useSignUp
});
//# sourceMappingURL=useSignUp.js.map