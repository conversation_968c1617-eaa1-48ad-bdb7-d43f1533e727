{"version": 3, "sources": ["../../../src/components/withSession.tsx"], "sourcesContent": ["import type { SessionResource } from '@clerk/types';\nimport React from 'react';\n\nimport { useSessionContext } from '../contexts/SessionContext';\nimport { hocChildrenNotAFunctionError } from '../errors';\n\nexport const withSession = <P extends { session: SessionResource }>(\n  Component: React.ComponentType<P>,\n  displayName?: string,\n) => {\n  displayName = displayName || Component.displayName || Component.name || 'Component';\n  Component.displayName = displayName;\n  const HOC: React.FC<Omit<P, 'session'>> = (props: Omit<P, 'session'>) => {\n    const session = useSessionContext();\n\n    if (!session) {\n      return null;\n    }\n\n    return (\n      <Component\n        {...(props as P)}\n        session={session}\n      />\n    );\n  };\n\n  HOC.displayName = `withSession(${displayName})`;\n  return HOC;\n};\n\nexport const WithSession: React.FC<{\n  children: (session: SessionResource) => React.ReactNode;\n}> = ({ children }) => {\n  const session = useSessionContext();\n\n  if (typeof children !== 'function') {\n    throw new Error(hocChildrenNotAFunctionError);\n  }\n\n  if (!session) {\n    return null;\n  }\n\n  return <>{children(session)}</>;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,mBAAkB;AAElB,4BAAkC;AAClC,oBAA6C;AAEtC,MAAM,cAAc,CACzB,WACA,gBACG;AACH,gBAAc,eAAe,UAAU,eAAe,UAAU,QAAQ;AACxE,YAAU,cAAc;AACxB,QAAM,MAAoC,CAAC,UAA8B;AACvE,UAAM,cAAU,yCAAkB;AAElC,QAAI,CAAC,SAAS;AACZ,aAAO;AAAA,IACT;AAEA,WACE,6BAAAA,QAAA;AAAA,MAAC;AAAA;AAAA,QACE,GAAI;AAAA,QACL;AAAA;AAAA,IACF;AAAA,EAEJ;AAEA,MAAI,cAAc,eAAe,WAAW;AAC5C,SAAO;AACT;AAEO,MAAM,cAER,CAAC,EAAE,SAAS,MAAM;AACrB,QAAM,cAAU,yCAAkB;AAElC,MAAI,OAAO,aAAa,YAAY;AAClC,UAAM,IAAI,MAAM,0CAA4B;AAAA,EAC9C;AAEA,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AAEA,SAAO,6BAAAA,QAAA,2BAAAA,QAAA,gBAAG,SAAS,OAAO,CAAE;AAC9B;", "names": ["React"]}