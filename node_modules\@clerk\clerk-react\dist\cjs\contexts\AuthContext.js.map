{"version": 3, "sources": ["../../../src/contexts/AuthContext.ts"], "sourcesContent": ["import { createContextAndHook } from '@clerk/shared/react';\nimport type { ActJWTClaim, MembershipRole, OrganizationCustomPermissionKey } from '@clerk/types';\n\nexport const [AuthContext, useAuthContext] = createContextAndHook<{\n  userId: string | null | undefined;\n  sessionId: string | null | undefined;\n  actor: ActJWTClaim | null | undefined;\n  orgId: string | null | undefined;\n  orgRole: MembershipRole | null | undefined;\n  orgSlug: string | null | undefined;\n  orgPermissions: OrganizationCustomPermissionKey[] | null | undefined;\n}>('AuthContext');\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAqC;AAG9B,MAAM,CAAC,aAAa,cAAc,QAAI,mCAQ1C,aAAa;", "names": []}