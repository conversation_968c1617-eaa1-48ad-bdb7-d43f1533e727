{"version": 3, "sources": ["../../../src/components/withClerk.tsx"], "sourcesContent": ["import type { LoadedClerk } from '@clerk/types';\nimport React from 'react';\n\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\nimport { LoadedGuarantee } from '../contexts/StructureContext';\nimport { hocChildrenNotAFunctionError } from '../errors';\n\nexport const withClerk = <P extends { clerk: LoadedClerk }>(\n  Component: React.ComponentType<P>,\n  displayName?: string,\n) => {\n  displayName = displayName || Component.displayName || Component.name || 'Component';\n  Component.displayName = displayName;\n  const HOC = (props: Omit<P, 'clerk'>) => {\n    const clerk = useIsomorphicClerkContext();\n\n    if (!clerk.loaded) {\n      return null;\n    }\n\n    return (\n      <LoadedGuarantee>\n        <Component\n          {...(props as P)}\n          clerk={clerk}\n        />\n      </LoadedGuarantee>\n    );\n  };\n  HOC.displayName = `withClerk(${displayName})`;\n  return HOC;\n};\n\nexport const WithClerk: React.FC<{\n  children: (clerk: LoadedClerk) => React.ReactNode;\n}> = ({ children }) => {\n  const clerk = useIsomorphicClerkContext();\n\n  if (typeof children !== 'function') {\n    throw new Error(hocChildrenNotAFunctionError);\n  }\n\n  if (!clerk.loaded) {\n    return null;\n  }\n\n  return <LoadedGuarantee>{children(clerk as unknown as LoadedClerk)}</LoadedGuarantee>;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,mBAAkB;AAElB,oCAA0C;AAC1C,8BAAgC;AAChC,oBAA6C;AAEtC,MAAM,YAAY,CACvB,WACA,gBACG;AACH,gBAAc,eAAe,UAAU,eAAe,UAAU,QAAQ;AACxE,YAAU,cAAc;AACxB,QAAM,MAAM,CAAC,UAA4B;AACvC,UAAM,YAAQ,yDAA0B;AAExC,QAAI,CAAC,MAAM,QAAQ;AACjB,aAAO;AAAA,IACT;AAEA,WACE,6BAAAA,QAAA,cAAC,+CACC,6BAAAA,QAAA;AAAA,MAAC;AAAA;AAAA,QACE,GAAI;AAAA,QACL;AAAA;AAAA,IACF,CACF;AAAA,EAEJ;AACA,MAAI,cAAc,aAAa,WAAW;AAC1C,SAAO;AACT;AAEO,MAAM,YAER,CAAC,EAAE,SAAS,MAAM;AACrB,QAAM,YAAQ,yDAA0B;AAExC,MAAI,OAAO,aAAa,YAAY;AAClC,UAAM,IAAI,MAAM,0CAA4B;AAAA,EAC9C;AAEA,MAAI,CAAC,MAAM,QAAQ;AACjB,WAAO;AAAA,EACT;AAEA,SAAO,6BAAAA,QAAA,cAAC,+CAAiB,SAAS,KAA+B,CAAE;AACrE;", "names": ["React"]}