"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var ClerkContextProvider_exports = {};
__export(ClerkContextProvider_exports, {
  ClerkContextProvider: () => ClerkContextProvider
});
module.exports = __toCommonJS(ClerkContextProvider_exports);
var import_deprecated = require("@clerk/shared/deprecated");
var import_react = __toESM(require("react"));
var import_isomorphicClerk = __toESM(require("../isomorphicClerk"));
var import_deriveState = require("../utils/deriveState");
var import_AuthContext = require("./AuthContext");
var import_ClientContext = require("./ClientContext");
var import_IsomorphicClerkContext = require("./IsomorphicClerkContext");
var import_OrganizationContext = require("./OrganizationContext");
var import_SessionContext = require("./SessionContext");
var import_UserContext = require("./UserContext");
function ClerkContextProvider(props) {
  const { isomorphicClerkOptions, initialState, children } = props;
  const { isomorphicClerk: clerk, loaded: clerkLoaded } = useLoadedIsomorphicClerk(isomorphicClerkOptions);
  if (isomorphicClerkOptions.frontendApi) {
    (0, import_deprecated.deprecated)("frontendApi", "Use `publishableKey` instead.");
  }
  const [state, setState] = import_react.default.useState({
    client: clerk.client,
    session: clerk.session,
    user: clerk.user,
    organization: clerk.organization,
    lastOrganizationInvitation: null,
    lastOrganizationMember: null
  });
  import_react.default.useEffect(() => {
    return clerk.addListener((e) => setState({ ...e }));
  }, []);
  const derivedState = (0, import_deriveState.deriveState)(clerkLoaded, state, initialState);
  const clerkCtx = import_react.default.useMemo(() => ({ value: clerk }), [clerkLoaded]);
  const clientCtx = import_react.default.useMemo(() => ({ value: state.client }), [state.client]);
  const {
    sessionId,
    session,
    userId,
    user,
    orgId,
    actor,
    lastOrganizationInvitation,
    lastOrganizationMember,
    organization,
    orgRole,
    orgSlug,
    orgPermissions
  } = derivedState;
  const authCtx = import_react.default.useMemo(() => {
    const value = { sessionId, userId, actor, orgId, orgRole, orgSlug, orgPermissions };
    return { value };
  }, [sessionId, userId, actor, orgId, orgRole, orgSlug]);
  const userCtx = import_react.default.useMemo(() => ({ value: user }), [userId, user]);
  const sessionCtx = import_react.default.useMemo(() => ({ value: session }), [sessionId, session]);
  const organizationCtx = import_react.default.useMemo(() => {
    const value = {
      organization,
      lastOrganizationInvitation,
      lastOrganizationMember
    };
    return { value };
  }, [orgId, organization, lastOrganizationInvitation, lastOrganizationMember]);
  return (
    // @ts-expect-error value passed is of type IsomorphicClerk where the context expects LoadedClerk
    /* @__PURE__ */ import_react.default.createElement(import_IsomorphicClerkContext.IsomorphicClerkContext.Provider, { value: clerkCtx }, /* @__PURE__ */ import_react.default.createElement(import_ClientContext.ClientContext.Provider, { value: clientCtx }, /* @__PURE__ */ import_react.default.createElement(import_SessionContext.SessionContext.Provider, { value: sessionCtx }, /* @__PURE__ */ import_react.default.createElement(import_OrganizationContext.OrganizationProvider, { ...organizationCtx.value }, /* @__PURE__ */ import_react.default.createElement(import_AuthContext.AuthContext.Provider, { value: authCtx }, /* @__PURE__ */ import_react.default.createElement(import_UserContext.UserContext.Provider, { value: userCtx }, children))))))
  );
}
const useLoadedIsomorphicClerk = (options) => {
  const [loaded, setLoaded] = import_react.default.useState(false);
  const isomorphicClerk = import_react.default.useMemo(() => import_isomorphicClerk.default.getOrCreateInstance(options), []);
  import_react.default.useEffect(() => {
    isomorphicClerk.__unstable__updateProps({ appearance: options.appearance });
  }, [options.appearance]);
  import_react.default.useEffect(() => {
    isomorphicClerk.__unstable__updateProps({ options });
  }, [options.localization]);
  import_react.default.useEffect(() => {
    isomorphicClerk.addOnLoaded(() => setLoaded(true));
  }, []);
  import_react.default.useEffect(() => {
    return () => {
      import_isomorphicClerk.default.clearInstance();
    };
  }, []);
  return { isomorphicClerk, loaded };
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  ClerkContextProvider
});
//# sourceMappingURL=ClerkContextProvider.js.map