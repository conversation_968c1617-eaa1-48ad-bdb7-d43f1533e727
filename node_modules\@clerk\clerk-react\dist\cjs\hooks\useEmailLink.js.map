{"version": 3, "sources": ["../../../src/hooks/useEmailLink.ts"], "sourcesContent": ["import type {\n  CreateEmailLinkFlowReturn,\n  EmailAddressResource,\n  SignInResource,\n  SignInStartEmailLinkFlowParams,\n  SignUpResource,\n  StartEmailLinkFlowParams,\n} from '@clerk/types';\nimport React from 'react';\n\ntype EmailLinkable = SignUpResource | EmailAddressResource | SignInResource;\ntype UseEmailLinkSignInReturn = CreateEmailLinkFlowReturn<SignInStartEmailLinkFlowParams, SignInResource>;\ntype UseEmailLinkSignUpReturn = CreateEmailLinkFlowReturn<StartEmailLinkFlowParams, SignUpResource>;\ntype UseEmailLinkEmailAddressReturn = CreateEmailLinkFlowReturn<StartEmailLinkFlowParams, EmailAddressResource>;\n\nfunction useEmailLink(resource: SignInResource): UseEmailLinkSignInReturn;\nfunction useEmailLink(resource: SignUpResource): UseEmailLinkSignUpReturn;\nfunction useEmailLink(resource: EmailAddressResource): UseEmailLinkEmailAddressReturn;\nfunction useEmailLink(\n  resource: EmailLinkable,\n): UseEmailLinkSignInReturn | UseEmailLinkSignUpReturn | UseEmailLinkEmailAddressReturn {\n  const { startEmailLinkFlow, cancelEmailLinkFlow } = React.useMemo(() => resource.createEmailLinkFlow(), [resource]);\n\n  React.useEffect(() => {\n    return cancelEmailLinkFlow;\n  }, []);\n\n  return {\n    startEmailLinkFlow,\n    cancelEmailLinkFlow,\n  } as UseEmailLinkSignInReturn | UseEmailLinkSignUpReturn | UseEmailLinkEmailAddressReturn;\n}\n\nexport { useEmailLink };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,mBAAkB;AAUlB,SAAS,aACP,UACsF;AACtF,QAAM,EAAE,oBAAoB,oBAAoB,IAAI,aAAAA,QAAM,QAAQ,MAAM,SAAS,oBAAoB,GAAG,CAAC,QAAQ,CAAC;AAElH,eAAAA,QAAM,UAAU,MAAM;AACpB,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AAEL,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;", "names": ["React"]}