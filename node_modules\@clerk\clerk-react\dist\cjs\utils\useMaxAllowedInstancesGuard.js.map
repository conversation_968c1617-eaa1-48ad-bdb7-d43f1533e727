{"version": 3, "sources": ["../../../src/utils/useMaxAllowedInstancesGuard.tsx"], "sourcesContent": ["import React from 'react';\n\nconst counts = new Map<string, number>();\n\nexport function useMaxAllowedInstancesGuard(name: string, error: string, maxCount = 1): void {\n  React.useEffect(() => {\n    const count = counts.get(name) || 0;\n    if (count == maxCount) {\n      throw new Error(error);\n    }\n    counts.set(name, count + 1);\n\n    return () => {\n      counts.set(name, (counts.get(name) || 1) - 1);\n    };\n  }, []);\n}\n\nexport function withMaxAllowedInstancesGuard<P>(\n  WrappedComponent: React.ComponentType<P>,\n  name: string,\n  error: string,\n): React.ComponentType<P> {\n  const displayName = WrappedComponent.displayName || WrappedComponent.name || name || 'Component';\n  const Hoc = (props: P) => {\n    useMaxAllowedInstancesGuard(name, error);\n    return <WrappedComponent {...(props as any)} />;\n  };\n  Hoc.displayName = `withMaxAllowedInstancesGuard(${displayName})`;\n  return Hoc;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAkB;AAElB,MAAM,SAAS,oBAAI,IAAoB;AAEhC,SAAS,4BAA4B,MAAc,OAAe,WAAW,GAAS;AAC3F,eAAAA,QAAM,UAAU,MAAM;AACpB,UAAM,QAAQ,OAAO,IAAI,IAAI,KAAK;AAClC,QAAI,SAAS,UAAU;AACrB,YAAM,IAAI,MAAM,KAAK;AAAA,IACvB;AACA,WAAO,IAAI,MAAM,QAAQ,CAAC;AAE1B,WAAO,MAAM;AACX,aAAO,IAAI,OAAO,OAAO,IAAI,IAAI,KAAK,KAAK,CAAC;AAAA,IAC9C;AAAA,EACF,GAAG,CAAC,CAAC;AACP;AAEO,SAAS,6BACd,kBACA,MACA,OACwB;AACxB,QAAM,cAAc,iBAAiB,eAAe,iBAAiB,QAAQ,QAAQ;AACrF,QAAM,MAAM,CAAC,UAAa;AACxB,gCAA4B,MAAM,KAAK;AACvC,WAAO,6BAAAA,QAAA,cAAC,oBAAkB,GAAI,OAAe;AAAA,EAC/C;AACA,MAAI,cAAc,gCAAgC,WAAW;AAC7D,SAAO;AACT;", "names": ["React"]}