"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var withClerk_exports = {};
__export(withClerk_exports, {
  WithClerk: () => WithClerk,
  withClerk: () => withClerk
});
module.exports = __toCommonJS(withClerk_exports);
var import_react = __toESM(require("react"));
var import_IsomorphicClerkContext = require("../contexts/IsomorphicClerkContext");
var import_StructureContext = require("../contexts/StructureContext");
var import_errors = require("../errors");
const withClerk = (Component, displayName) => {
  displayName = displayName || Component.displayName || Component.name || "Component";
  Component.displayName = displayName;
  const HOC = (props) => {
    const clerk = (0, import_IsomorphicClerkContext.useIsomorphicClerkContext)();
    if (!clerk.loaded) {
      return null;
    }
    return /* @__PURE__ */ import_react.default.createElement(import_StructureContext.LoadedGuarantee, null, /* @__PURE__ */ import_react.default.createElement(
      Component,
      {
        ...props,
        clerk
      }
    ));
  };
  HOC.displayName = `withClerk(${displayName})`;
  return HOC;
};
const WithClerk = ({ children }) => {
  const clerk = (0, import_IsomorphicClerkContext.useIsomorphicClerkContext)();
  if (typeof children !== "function") {
    throw new Error(import_errors.hocChildrenNotAFunctionError);
  }
  if (!clerk.loaded) {
    return null;
  }
  return /* @__PURE__ */ import_react.default.createElement(import_StructureContext.LoadedGuarantee, null, children(clerk));
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  WithClerk,
  withClerk
});
//# sourceMappingURL=withClerk.js.map