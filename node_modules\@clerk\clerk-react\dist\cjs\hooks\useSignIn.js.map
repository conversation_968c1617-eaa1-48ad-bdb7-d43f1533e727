{"version": 3, "sources": ["../../../src/hooks/useSignIn.ts"], "sourcesContent": ["import type { SetActive, SetSession, SignInResource } from '@clerk/types';\n\nimport { useClientContext } from '../contexts/ClientContext';\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\n\ntype UseSignInReturn =\n  | {\n      isLoaded: false;\n      signIn: undefined;\n      /**\n       * @deprecated This method is deprecated and will be removed in the future. Use {@link Clerk.setActive} instead\n       * Set the current session explicitly. Setting the session to `null` unsets the active session and signs out the user.\n       * @param session Passed session resource object, session id (string version) or null\n       * @param beforeEmit Callback run just before the active session is set to the passed object. Can be used to hook up for pre-navigation actions.\n       */\n      setSession: undefined;\n      setActive: undefined;\n    }\n  | {\n      isLoaded: true;\n      signIn: SignInResource;\n      /**\n       * @deprecated This method is deprecated and will be removed in the future. Use {@link Clerk.setActive} instead\n       * Set the current session explicitly. Setting the session to `null` unsets the active session and signs out the user.\n       * @param session Passed session resource object, session id (string version) or null\n       * @param beforeEmit Callback run just before the active session is set to the passed object. Can be used to hook up for pre-navigation actions.\n       */\n      setSession: SetSession;\n      setActive: SetActive;\n    };\n\ntype UseSignIn = () => UseSignInReturn;\n\nexport const useSignIn: UseSignIn = () => {\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const client = useClientContext();\n\n  if (!client) {\n    return { isLoaded: false, signIn: undefined, setSession: undefined, setActive: undefined };\n  }\n\n  return {\n    isLoaded: true,\n    signIn: client.signIn,\n    setSession: isomorphicClerk.setSession,\n    setActive: isomorphicClerk.setActive,\n  };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,2BAAiC;AACjC,oCAA0C;AA8BnC,MAAM,YAAuB,MAAM;AACxC,QAAM,sBAAkB,yDAA0B;AAClD,QAAM,aAAS,uCAAiB;AAEhC,MAAI,CAAC,QAAQ;AACX,WAAO,EAAE,UAAU,OAAO,QAAQ,QAAW,YAAY,QAAW,WAAW,OAAU;AAAA,EAC3F;AAEA,SAAO;AAAA,IACL,UAAU;AAAA,IACV,QAAQ,OAAO;AAAA,IACf,YAAY,gBAAgB;AAAA,IAC5B,WAAW,gBAAgB;AAAA,EAC7B;AACF;", "names": []}