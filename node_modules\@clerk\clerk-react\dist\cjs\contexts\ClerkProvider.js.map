{"version": 3, "sources": ["../../../src/contexts/ClerkProvider.tsx"], "sourcesContent": ["import { isLegacyFrontendApiKey, isPublishableKey } from '@clerk/shared/keys';\nimport type { InitialState } from '@clerk/types';\nimport React from 'react';\n\nimport { multipleClerkProvidersError } from '../errors';\nimport type { IsomorphicClerkOptions } from '../types';\nimport { __internal__setErrorThrowerOptions, errorThrower, withMaxAllowedInstancesGuard } from '../utils';\nimport { ClerkContextProvider } from './ClerkContextProvider';\nimport { StructureContext, StructureContextStates } from './StructureContext';\n\n__internal__setErrorThrowerOptions({\n  packageName: '@clerk/clerk-react',\n});\n\nexport type ClerkProviderProps = IsomorphicClerkOptions & {\n  children: React.ReactNode;\n  initialState?: InitialState;\n};\n\nfunction ClerkProviderBase(props: ClerkProviderProps): JSX.Element {\n  const { initialState, children, ...restIsomorphicClerkOptions } = props;\n  const { frontendApi = '', publishableKey = '', Clerk: userInitialisedClerk } = restIsomorphicClerkOptions;\n\n  if (!userInitialisedClerk) {\n    if (!publishableKey && !frontendApi) {\n      errorThrower.throwMissingPublishableKeyError();\n    } else if (publishableKey && !isPublishableKey(publishableKey)) {\n      errorThrower.throwInvalidPublishableKeyError({ key: publishableKey });\n    } else if (!publishableKey && frontendApi && !isLegacyFrontendApiKey(frontendApi)) {\n      errorThrower.throwInvalidFrontendApiError({ key: frontendApi });\n    }\n  }\n\n  return (\n    <StructureContext.Provider value={StructureContextStates.noGuarantees}>\n      <ClerkContextProvider\n        initialState={initialState}\n        isomorphicClerkOptions={restIsomorphicClerkOptions}\n      >\n        {children}\n      </ClerkContextProvider>\n    </StructureContext.Provider>\n  );\n}\n\nconst ClerkProvider = withMaxAllowedInstancesGuard(ClerkProviderBase, 'ClerkProvider', multipleClerkProvidersError);\n\nClerkProvider.displayName = 'ClerkProvider';\n\nexport { ClerkProvider, __internal__setErrorThrowerOptions };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAyD;AAEzD,mBAAkB;AAElB,oBAA4C;AAE5C,mBAA+F;AAC/F,kCAAqC;AACrC,8BAAyD;AAAA,IAEzD,iDAAmC;AAAA,EACjC,aAAa;AACf,CAAC;AAOD,SAAS,kBAAkB,OAAwC;AACjE,QAAM,EAAE,cAAc,UAAU,GAAG,2BAA2B,IAAI;AAClE,QAAM,EAAE,cAAc,IAAI,iBAAiB,IAAI,OAAO,qBAAqB,IAAI;AAE/E,MAAI,CAAC,sBAAsB;AACzB,QAAI,CAAC,kBAAkB,CAAC,aAAa;AACnC,gCAAa,gCAAgC;AAAA,IAC/C,WAAW,kBAAkB,KAAC,8BAAiB,cAAc,GAAG;AAC9D,gCAAa,gCAAgC,EAAE,KAAK,eAAe,CAAC;AAAA,IACtE,WAAW,CAAC,kBAAkB,eAAe,KAAC,oCAAuB,WAAW,GAAG;AACjF,gCAAa,6BAA6B,EAAE,KAAK,YAAY,CAAC;AAAA,IAChE;AAAA,EACF;AAEA,SACE,6BAAAA,QAAA,cAAC,yCAAiB,UAAjB,EAA0B,OAAO,+CAAuB,gBACvD,6BAAAA,QAAA;AAAA,IAAC;AAAA;AAAA,MACC;AAAA,MACA,wBAAwB;AAAA;AAAA,IAEvB;AAAA,EACH,CACF;AAEJ;AAEA,MAAM,oBAAgB,2CAA6B,mBAAmB,iBAAiB,yCAA2B;AAElH,cAAc,cAAc;", "names": ["React"]}