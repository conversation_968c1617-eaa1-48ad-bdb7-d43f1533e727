{"version": 3, "sources": ["../../../src/contexts/ClerkContextProvider.tsx"], "sourcesContent": ["import { deprecated } from '@clerk/shared/deprecated';\nimport type { ClientResource, InitialState, Resources } from '@clerk/types';\nimport React from 'react';\n\nimport IsomorphicClerk from '../isomorphicClerk';\nimport type { IsomorphicClerkOptions } from '../types';\nimport { deriveState } from '../utils/deriveState';\nimport { AuthContext } from './AuthContext';\nimport { ClientContext } from './ClientContext';\nimport { IsomorphicClerkContext } from './IsomorphicClerkContext';\nimport { OrganizationProvider } from './OrganizationContext';\nimport { SessionContext } from './SessionContext';\nimport { UserContext } from './UserContext';\n\ntype ClerkContextProvider = {\n  isomorphicClerkOptions: IsomorphicClerkOptions;\n  initialState: InitialState | undefined;\n  children: React.ReactNode;\n};\n\nexport type ClerkContextProviderState = Resources;\n\nexport function ClerkContextProvider(props: ClerkContextProvider): JSX.Element | null {\n  const { isomorphicClerkOptions, initialState, children } = props;\n  const { isomorphicClerk: clerk, loaded: clerkLoaded } = useLoadedIsomorphicClerk(isomorphicClerkOptions);\n\n  if (isomorphicClerkOptions.frontendApi) {\n    deprecated('frontendApi', 'Use `publishableKey` instead.');\n  }\n\n  const [state, setState] = React.useState<ClerkContextProviderState>({\n    client: clerk.client as ClientResource,\n    session: clerk.session,\n    user: clerk.user,\n    organization: clerk.organization,\n    lastOrganizationInvitation: null,\n    lastOrganizationMember: null,\n  });\n\n  React.useEffect(() => {\n    return clerk.addListener(e => setState({ ...e }));\n  }, []);\n\n  const derivedState = deriveState(clerkLoaded, state, initialState);\n  const clerkCtx = React.useMemo(() => ({ value: clerk }), [clerkLoaded]);\n  const clientCtx = React.useMemo(() => ({ value: state.client }), [state.client]);\n\n  const {\n    sessionId,\n    session,\n    userId,\n    user,\n    orgId,\n    actor,\n    lastOrganizationInvitation,\n    lastOrganizationMember,\n    organization,\n    orgRole,\n    orgSlug,\n    orgPermissions,\n  } = derivedState;\n\n  const authCtx = React.useMemo(() => {\n    const value = { sessionId, userId, actor, orgId, orgRole, orgSlug, orgPermissions };\n    return { value };\n  }, [sessionId, userId, actor, orgId, orgRole, orgSlug]);\n  const userCtx = React.useMemo(() => ({ value: user }), [userId, user]);\n  const sessionCtx = React.useMemo(() => ({ value: session }), [sessionId, session]);\n  const organizationCtx = React.useMemo(() => {\n    const value = {\n      organization: organization,\n      lastOrganizationInvitation: lastOrganizationInvitation,\n      lastOrganizationMember: lastOrganizationMember,\n    };\n    return { value };\n  }, [orgId, organization, lastOrganizationInvitation, lastOrganizationMember]);\n\n  return (\n    // @ts-expect-error value passed is of type IsomorphicClerk where the context expects LoadedClerk\n    <IsomorphicClerkContext.Provider value={clerkCtx}>\n      <ClientContext.Provider value={clientCtx}>\n        <SessionContext.Provider value={sessionCtx}>\n          <OrganizationProvider {...organizationCtx.value}>\n            <AuthContext.Provider value={authCtx}>\n              <UserContext.Provider value={userCtx}>{children}</UserContext.Provider>\n            </AuthContext.Provider>\n          </OrganizationProvider>\n        </SessionContext.Provider>\n      </ClientContext.Provider>\n    </IsomorphicClerkContext.Provider>\n  );\n}\n\nconst useLoadedIsomorphicClerk = (options: IsomorphicClerkOptions) => {\n  const [loaded, setLoaded] = React.useState(false);\n  const isomorphicClerk = React.useMemo(() => IsomorphicClerk.getOrCreateInstance(options), []);\n\n  React.useEffect(() => {\n    isomorphicClerk.__unstable__updateProps({ appearance: options.appearance });\n  }, [options.appearance]);\n\n  React.useEffect(() => {\n    isomorphicClerk.__unstable__updateProps({ options });\n  }, [options.localization]);\n\n  React.useEffect(() => {\n    isomorphicClerk.addOnLoaded(() => setLoaded(true));\n  }, []);\n\n  React.useEffect(() => {\n    return () => {\n      IsomorphicClerk.clearInstance();\n    };\n  }, []);\n\n  return { isomorphicClerk, loaded };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAA2B;AAE3B,mBAAkB;AAElB,6BAA4B;AAE5B,yBAA4B;AAC5B,yBAA4B;AAC5B,2BAA8B;AAC9B,oCAAuC;AACvC,iCAAqC;AACrC,4BAA+B;AAC/B,yBAA4B;AAUrB,SAAS,qBAAqB,OAAiD;AACpF,QAAM,EAAE,wBAAwB,cAAc,SAAS,IAAI;AAC3D,QAAM,EAAE,iBAAiB,OAAO,QAAQ,YAAY,IAAI,yBAAyB,sBAAsB;AAEvG,MAAI,uBAAuB,aAAa;AACtC,sCAAW,eAAe,+BAA+B;AAAA,EAC3D;AAEA,QAAM,CAAC,OAAO,QAAQ,IAAI,aAAAA,QAAM,SAAoC;AAAA,IAClE,QAAQ,MAAM;AAAA,IACd,SAAS,MAAM;AAAA,IACf,MAAM,MAAM;AAAA,IACZ,cAAc,MAAM;AAAA,IACpB,4BAA4B;AAAA,IAC5B,wBAAwB;AAAA,EAC1B,CAAC;AAED,eAAAA,QAAM,UAAU,MAAM;AACpB,WAAO,MAAM,YAAY,OAAK,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC;AAAA,EAClD,GAAG,CAAC,CAAC;AAEL,QAAM,mBAAe,gCAAY,aAAa,OAAO,YAAY;AACjE,QAAM,WAAW,aAAAA,QAAM,QAAQ,OAAO,EAAE,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC;AACtE,QAAM,YAAY,aAAAA,QAAM,QAAQ,OAAO,EAAE,OAAO,MAAM,OAAO,IAAI,CAAC,MAAM,MAAM,CAAC;AAE/E,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,QAAM,UAAU,aAAAA,QAAM,QAAQ,MAAM;AAClC,UAAM,QAAQ,EAAE,WAAW,QAAQ,OAAO,OAAO,SAAS,SAAS,eAAe;AAClF,WAAO,EAAE,MAAM;AAAA,EACjB,GAAG,CAAC,WAAW,QAAQ,OAAO,OAAO,SAAS,OAAO,CAAC;AACtD,QAAM,UAAU,aAAAA,QAAM,QAAQ,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC,QAAQ,IAAI,CAAC;AACrE,QAAM,aAAa,aAAAA,QAAM,QAAQ,OAAO,EAAE,OAAO,QAAQ,IAAI,CAAC,WAAW,OAAO,CAAC;AACjF,QAAM,kBAAkB,aAAAA,QAAM,QAAQ,MAAM;AAC1C,UAAM,QAAQ;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,WAAO,EAAE,MAAM;AAAA,EACjB,GAAG,CAAC,OAAO,cAAc,4BAA4B,sBAAsB,CAAC;AAE5E;AAAA;AAAA,IAEE,6BAAAA,QAAA,cAAC,qDAAuB,UAAvB,EAAgC,OAAO,YACtC,6BAAAA,QAAA,cAAC,mCAAc,UAAd,EAAuB,OAAO,aAC7B,6BAAAA,QAAA,cAAC,qCAAe,UAAf,EAAwB,OAAO,cAC9B,6BAAAA,QAAA,cAAC,mDAAsB,GAAG,gBAAgB,SACxC,6BAAAA,QAAA,cAAC,+BAAY,UAAZ,EAAqB,OAAO,WAC3B,6BAAAA,QAAA,cAAC,+BAAY,UAAZ,EAAqB,OAAO,WAAU,QAAS,CAClD,CACF,CACF,CACF,CACF;AAAA;AAEJ;AAEA,MAAM,2BAA2B,CAAC,YAAoC;AACpE,QAAM,CAAC,QAAQ,SAAS,IAAI,aAAAA,QAAM,SAAS,KAAK;AAChD,QAAM,kBAAkB,aAAAA,QAAM,QAAQ,MAAM,uBAAAC,QAAgB,oBAAoB,OAAO,GAAG,CAAC,CAAC;AAE5F,eAAAD,QAAM,UAAU,MAAM;AACpB,oBAAgB,wBAAwB,EAAE,YAAY,QAAQ,WAAW,CAAC;AAAA,EAC5E,GAAG,CAAC,QAAQ,UAAU,CAAC;AAEvB,eAAAA,QAAM,UAAU,MAAM;AACpB,oBAAgB,wBAAwB,EAAE,QAAQ,CAAC;AAAA,EACrD,GAAG,CAAC,QAAQ,YAAY,CAAC;AAEzB,eAAAA,QAAM,UAAU,MAAM;AACpB,oBAAgB,YAAY,MAAM,UAAU,IAAI,CAAC;AAAA,EACnD,GAAG,CAAC,CAAC;AAEL,eAAAA,QAAM,UAAU,MAAM;AACpB,WAAO,MAAM;AACX,6BAAAC,QAAgB,cAAc;AAAA,IAChC;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,SAAO,EAAE,iBAAiB,OAAO;AACnC;", "names": ["React", "IsomorphicClerk"]}